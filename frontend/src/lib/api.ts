import axios, { AxiosInstance, AxiosResponse } from 'axios';
import type {
  ApiResponse,
  LoginRequest,
  CreateUserRequest,
  LoginResponse,
  User,
  Role,
  SkillGroup,
  CreateRoleRequest,
  RoleDependencyInfo,
  CreateSkillGroupRequest,
  SkillGroupDependencyInfo,
  Project,
  CreateProjectRequest,
  Part,
  CreatePartRequest,
  Machine,
  CreateMachineRequest,
  WorkOrder,
  CreateWorkOrderRequest,
  PlanTask,
  PlanTaskWithDetails,
  PlanTaskSearchResult,
  CreatePlanTaskRequest,
  UpdatePlanTaskRequest,
  ReschedulePlanTaskRequest,
  CreatePlanTasksFromWorkOrderRequest,
  GanttChartData,
  ExecutionLog,
  TaskExecutionRequest,
  QualityInspection,
  CreateQualityInspectionRequest,
  DashboardOverview,
  ProductionSummary,
  SearchParams,
  ProjectBom,
  CreateProjectBomRequest,
  Routing,
  RoutingWithPartInfo,
  CreateRoutingRequest,
  UpdateRoutingRequest,
  PartRoutingSteps,
  RoutingQuery,
  ReorderStepsRequest,
  CopyRoutingRequest,
} from '@/types/api';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          window.location.href = '/login';
        }
        // 移除message调用，让组件层处理错误显示
        return Promise.reject(error);
      }
    );
  }

  // Authentication APIs
  async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await this.client.post<LoginResponse>('/auth/login', data);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<User>('/auth/me');
    return response.data;
  }

  // User Management APIs
  async getUsers(params?: SearchParams): Promise<User[]> {
    const response = await this.client.get<{ users: User[] }>('/users', { params });
    return response.data.users;
  }

  async getUserById(id: number): Promise<User> {
    const response = await this.client.get<{ user: User }>(`/users/${id}`);
    return response.data.user;
  }

  async createUser(data: CreateUserRequest): Promise<User> {
    const response = await this.client.post<{ message: string; user: User }>('/auth/users', data);
    return response.data.user;
  }

  async updateUserStatus(id: number, isActive: boolean): Promise<void> {
    await this.client.post(`/users/${id}/status`, { is_active: isActive });
  }

  async updateUserRoles(id: number, roleIds: number[]): Promise<void> {
    await this.client.post(`/users/${id}/roles`, { role_ids: roleIds });
  }

  async updateUserSkills(id: number, skillGroupIds: number[]): Promise<void> {
    await this.client.post(`/users/${id}/skills`, { skill_group_ids: skillGroupIds });
  }

  async deleteUser(id: number): Promise<void> {
    await this.client.delete(`/users/${id}`);
  }

  // Role and Skill Group APIs
  async getRoles(): Promise<Role[]> {
    const response = await this.client.get<{ roles: Role[] }>('/auth/roles');
    return response.data.roles;
  }

  async getSkillGroups(): Promise<SkillGroup[]> {
    const response = await this.client.get<{ skill_groups: SkillGroup[] }>('/auth/skill-groups');
    return response.data.skill_groups;
  }

  // Role Management APIs
  async createRole(data: CreateRoleRequest): Promise<Role> {
    const response = await this.client.post<{ message: string; role: Role }>('/roles', data);
    return response.data.role;
  }

  async updateRole(id: number, data: Partial<CreateRoleRequest>): Promise<Role> {
    const response = await this.client.put<{ message: string; role: Role }>(`/roles/${id}`, data);
    return response.data.role;
  }

  async deleteRole(id: number, replacementRoleId?: number): Promise<void> {
    const body = replacementRoleId ? { replacement_role_id: replacementRoleId } : {};
    await this.client.delete(`/roles/${id}`, { data: body });
  }

  async checkRoleDependencies(id: number): Promise<RoleDependencyInfo> {
    const response = await this.client.get<RoleDependencyInfo>(`/roles/${id}/dependencies`);
    return response.data;
  }

  // Skill Group Management APIs
  async createSkillGroup(data: CreateSkillGroupRequest): Promise<SkillGroup> {
    const response = await this.client.post<{ message: string; skill_group: SkillGroup }>('/skill-groups', data);
    return response.data.skill_group;
  }

  async updateSkillGroup(id: number, data: Partial<CreateSkillGroupRequest>): Promise<SkillGroup> {
    const response = await this.client.put<{ message: string; skill_group: SkillGroup }>(`/skill-groups/${id}`, data);
    return response.data.skill_group;
  }

  async deleteSkillGroup(id: number, replacementSkillGroupId?: number): Promise<void> {
    const body = replacementSkillGroupId ? { replacement_skill_group_id: replacementSkillGroupId } : {};
    await this.client.delete(`/skill-groups/${id}`, { data: body });
  }

  async checkSkillGroupDependencies(id: number): Promise<SkillGroupDependencyInfo> {
    const response = await this.client.get<SkillGroupDependencyInfo>(`/skill-groups/${id}/dependencies`);
    return response.data;
  }

  // Project Management APIs
  async getProjects(params?: SearchParams): Promise<Project[]> {
    const response = await this.client.get<{ projects: Project[] }>('/projects', { params });
    return response.data.projects;
  }

  async getProjectById(id: number): Promise<Project> {
    const response = await this.client.get<{ project: Project }>(`/projects/${id}`);
    return response.data.project;
  }

  async createProject(data: CreateProjectRequest): Promise<Project> {
    const response = await this.client.post<{ message: string; project: Project }>('/projects', data);
    return response.data.project;
  }

  async updateProject(id: number, data: Partial<CreateProjectRequest>): Promise<Project> {
    const response = await this.client.put<{ message: string; project: Project }>(`/projects/${id}`, data);
    return response.data.project;
  }

  async deleteProject(id: number): Promise<void> {
    await this.client.delete(`/projects/${id}`);
  }

  // Parts Management APIs
  async getParts(params?: SearchParams): Promise<Part[]> {
    const response = await this.client.get<{ data: { parts: Part[] } }>('/parts', { params });
    return response.data.data.parts;
  }

  async getPartById(id: number): Promise<Part> {
    const response = await this.client.get<{ part: Part }>(`/parts/${id}`);
    return response.data.part;
  }

  async createPart(data: CreatePartRequest): Promise<Part> {
    const response = await this.client.post<{ message: string; part: Part }>('/parts', data);
    return response.data.part;
  }

  async updatePart(id: number, data: Partial<CreatePartRequest>): Promise<Part> {
    const response = await this.client.put<{ message: string; part: Part }>(`/parts/${id}`, data);
    return response.data.part;
  }

  async deletePart(id: number): Promise<void> {
    await this.client.delete(`/parts/${id}`);
  }

  // Machine Management APIs
  async getMachines(params?: SearchParams): Promise<Machine[]> {
    const response = await this.client.get<{ data: { machines: Machine[] } }>('/machines', { params });
    return response.data.data.machines;
  }

  async getMachineById(id: number): Promise<Machine> {
    const response = await this.client.get<{ machine: Machine }>(`/machines/${id}`);
    return response.data.machine;
  }

  async createMachine(data: CreateMachineRequest): Promise<Machine> {
    const response = await this.client.post<{ message: string; machine: Machine }>('/machines', data);
    return response.data.machine;
  }

  async updateMachine(id: number, data: Partial<CreateMachineRequest>): Promise<Machine> {
    const response = await this.client.put<{ message: string; machine: Machine }>(`/machines/${id}`, data);
    return response.data.machine;
  }

  async deleteMachine(id: number): Promise<void> {
    await this.client.delete(`/machines/${id}`);
  }

  // Work Order APIs
  async getWorkOrders(params?: SearchParams): Promise<WorkOrder[]> {
    const response = await this.client.get<{ data: { work_orders: WorkOrder[] } }>('/work-orders', { params });
    return response.data.data.work_orders;
  }

  async getWorkOrderById(id: number): Promise<WorkOrder> {
    const response = await this.client.get<{ work_order: WorkOrder }>(`/work-orders/${id}`);
    return response.data.work_order;
  }

  async createWorkOrder(data: CreateWorkOrderRequest): Promise<WorkOrder> {
    const response = await this.client.post<{ message: string; work_order: WorkOrder }>('/work-orders', data);
    return response.data.work_order;
  }

  async updateWorkOrder(id: number, data: Partial<CreateWorkOrderRequest>): Promise<WorkOrder> {
    const response = await this.client.put<{ message: string; work_order: WorkOrder }>(`/work-orders/${id}`, data);
    return response.data.work_order;
  }

  async deleteWorkOrder(id: number): Promise<void> {
    await this.client.delete(`/work-orders/${id}`);
  }

  // Plan Task APIs
  async getPlanTasks(params?: SearchParams): Promise<PlanTaskWithDetails[]> {
    const response = await this.client.get<PlanTaskSearchResult>('/plan-tasks', { params });
    return response.data.plan_tasks;
  }

  async getPlanTaskById(id: number): Promise<PlanTaskWithDetails> {
    const response = await this.client.get<{ plan_task: PlanTaskWithDetails }>(`/plan-tasks/${id}`);
    return response.data.plan_task;
  }

  async createPlanTask(data: CreatePlanTaskRequest): Promise<PlanTask> {
    const response = await this.client.post<{ message: string; plan_task: PlanTask }>('/plan-tasks', data);
    return response.data.plan_task;
  }

  async updatePlanTask(id: number, data: UpdatePlanTaskRequest): Promise<PlanTask> {
    const response = await this.client.put<{ message: string; plan_task: PlanTask }>(`/plan-tasks/${id}`, data);
    return response.data.plan_task;
  }

  async deletePlanTask(id: number): Promise<void> {
    await this.client.delete(`/plan-tasks/${id}`);
  }

  async updatePlanTaskStatus(id: number, status: string): Promise<void> {
    await this.client.post(`/plan-tasks/${id}/status`, { status });
  }

  async reschedulePlanTask(id: number, data: ReschedulePlanTaskRequest): Promise<PlanTask> {
    const response = await this.client.post<{ message: string; plan_task: PlanTask }>(`/plan-tasks/${id}/reschedule`, data);
    return response.data.plan_task;
  }

  async createPlanTasksFromWorkOrder(workOrderId: number, data: CreatePlanTasksFromWorkOrderRequest): Promise<PlanTask[]> {
    const response = await this.client.post<{ message: string; plan_tasks: PlanTask[] }>(`/work-orders/${workOrderId}/plan-tasks`, data);
    return response.data.plan_tasks;
  }

  async getGanttChartData(startDate: string, endDate: string): Promise<GanttChartData> {
    const response = await this.client.get<GanttChartData>('/planning/gantt', {
      params: { start_date: startDate, end_date: endDate }
    });
    return response.data;
  }

  // Execution APIs
  async getExecutionLogs(params?: SearchParams): Promise<ExecutionLog[]> {
    const response = await this.client.get<{ data: { logs: ExecutionLog[] } }>('/execution/logs', { params });
    return response.data.data.logs;
  }

  async startTask(data: TaskExecutionRequest): Promise<void> {
    await this.client.post('/execution/tasks/start', data);
  }

  async completeTask(data: TaskExecutionRequest): Promise<void> {
    await this.client.post('/execution/tasks/complete', data);
  }

  async pauseTask(data: TaskExecutionRequest): Promise<void> {
    await this.client.post('/execution/tasks/pause', data);
  }

  async resumeTask(data: TaskExecutionRequest): Promise<void> {
    await this.client.post('/execution/tasks/resume', data);
  }

  async getMySkillGroupTasks(): Promise<PlanTaskWithDetails[]> {
    const response = await this.client.get<{ tasks: PlanTaskWithDetails[] }>('/execution/my-tasks');
    return response.data.tasks;
  }

  async getShopFloorDashboard(skillGroupId?: number): Promise<any> {
    const params = skillGroupId ? { skill_group_id: skillGroupId } : {};
    const response = await this.client.get('/execution/shop-floor-dashboard', { params });
    return response.data;
  }

  // Quality APIs
  async getQualityInspections(params?: SearchParams): Promise<QualityInspection[]> {
    const response = await this.client.get<{ data: { inspections: QualityInspection[] } }>('/quality/inspections', { params });
    return response.data.data.inspections;
  }

  async createQualityInspection(data: CreateQualityInspectionRequest): Promise<QualityInspection> {
    const response = await this.client.post<{ message: string; inspection: QualityInspection }>('/quality/inspections', data);
    return response.data.inspection;
  }

  // BOM APIs
  async getProjectBom(projectId: number): Promise<ProjectBom[]> {
    const response = await this.client.get<{ bom_items: ProjectBom[] }>(`/projects/${projectId}/bom`);
    return response.data.bom_items;
  }

  async getAllProjectBoms(): Promise<ProjectBom[]> {
    // Get all projects first, then get their BOMs
    const projects = await this.getProjects();
    const allBoms: ProjectBom[] = [];

    for (const project of projects) {
      try {
        const boms = await this.getProjectBom(project.id);
        // Add project name to each BOM item for display
        const bomsWithProjectName = boms.map(bom => ({
          ...bom,
          project_name: project.project_name
        }));
        allBoms.push(...bomsWithProjectName);
      } catch (error) {
        console.warn(`Failed to fetch BOM for project ${project.id}:`, error);
      }
    }

    return allBoms;
  }

  async createProjectBom(projectId: number, data: CreateProjectBomRequest): Promise<ProjectBom> {
    const response = await this.client.post<{ message: string; bom_item: ProjectBom }>(`/projects/${projectId}/bom`, data);
    return response.data.bom_item;
  }

  async updateProjectBom(bomId: number, data: Partial<CreateProjectBomRequest>): Promise<ProjectBom> {
    const response = await this.client.put<{ message: string; bom_item: ProjectBom }>(`/bom/${bomId}`, data);
    return response.data.bom_item;
  }

  async deleteProjectBom(bomId: number): Promise<void> {
    await this.client.delete(`/bom/${bomId}`);
  }

  // Dashboard APIs
  async getDashboardOverview(): Promise<DashboardOverview> {
    const response = await this.client.get<DashboardOverview>('/dashboard/overview');
    return response.data;
  }

  async getProductionSummary(): Promise<ProductionSummary> {
    const response = await this.client.get<ProductionSummary>('/dashboard/production-summary');
    return response.data;
  }

  // Routing APIs
  async getRoutings(params?: RoutingQuery): Promise<RoutingWithPartInfo[]> {
    const response = await this.client.get<{ routings: RoutingWithPartInfo[] }>('/routings', { params });
    return response.data.routings;
  }

  async getRoutingById(id: number): Promise<RoutingWithPartInfo> {
    const response = await this.client.get<{ routing: RoutingWithPartInfo }>(`/routings/${id}`);
    return response.data.routing;
  }

  async createRouting(data: CreateRoutingRequest): Promise<Routing> {
    const response = await this.client.post<{ message: string; routing: Routing }>('/routings', data);
    return response.data.routing;
  }

  async updateRouting(id: number, data: UpdateRoutingRequest): Promise<Routing> {
    const response = await this.client.put<{ message: string; routing: Routing }>(`/routings/${id}`, data);
    return response.data.routing;
  }

  async deleteRouting(id: number): Promise<void> {
    await this.client.delete(`/routings/${id}`);
  }

  async getPartRouting(partId: number): Promise<PartRoutingSteps> {
    const response = await this.client.get<{ part_routing: PartRoutingSteps }>(`/parts/${partId}/routing`);
    return response.data.part_routing;
  }

  async reorderRoutingSteps(partId: number, data: ReorderStepsRequest): Promise<void> {
    await this.client.post(`/parts/${partId}/routing/reorder`, data);
  }

  async copyRouting(sourcePartId: number, data: CopyRoutingRequest): Promise<void> {
    await this.client.post(`/parts/${sourcePartId}/routing/copy`, data);
  }
}

export const apiClient = new ApiClient();
