import React, { useState, useMemo } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Popconfirm,
  Typography,
  Card,
  Tag,
  Tabs,
  Badge,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SettingOutlined, ToolOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import type { Machine, CreateMachineRequest, SkillGroup } from '@/types/api';

const { Title } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

const Machines: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingMachine, setEditingMachine] = useState<Machine | null>(null);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  // 技能组名称中文映射
  const getSkillGroupDisplayName = (groupName: string) => {
    const skillGroupNameMap: Record<string, string> = {
      'CNC Machining': 'CNC加工',
      'Milling': '铣削加工',
      'Turning': '车削加工',
      'Grinding': '磨削加工',
      'Assembly': '装配',
      'Quality Control': '质量控制',
    };
    return skillGroupNameMap[groupName] || groupName;
  };

  const { data: machines, isLoading } = useQuery(
    'machines',
    () => apiClient.getMachines()
  );

  const { data: skillGroups } = useQuery(
    'skill-groups',
    () => apiClient.getSkillGroups()
  );

  // 按技能组分组设备
  const machinesBySkillGroup = useMemo(() => {
    if (!machines || !skillGroups) return {};

    const grouped: Record<string, Machine[]> = {};

    // 初始化所有技能组
    skillGroups.forEach(group => {
      grouped[group.id.toString()] = [];
    });

    // 分组设备
    machines.forEach(machine => {
      const groupId = machine.skill_group_id.toString();
      if (grouped[groupId]) {
        grouped[groupId].push(machine);
      }
    });

    return grouped;
  }, [machines, skillGroups]);

  // 获取当前标签页的设备数据
  const getCurrentTabMachines = () => {
    if (activeTab === 'all') {
      return machines || [];
    }
    return machinesBySkillGroup[activeTab] || [];
  };

  const createMutation = useMutation(
    (data: CreateMachineRequest) => apiClient.createMachine(data),
    {
      onSuccess: () => {
        success('设备创建成功');
        queryClient.invalidateQueries('machines');
        setIsModalVisible(false);
        form.resetFields();
      },
    }
  );

  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<CreateMachineRequest> }) =>
      apiClient.updateMachine(id, data),
    {
      onSuccess: () => {
        success('设备更新成功');
        queryClient.invalidateQueries('machines');
        setIsModalVisible(false);
        setEditingMachine(null);
        form.resetFields();
      },
    }
  );

  const deleteMutation = useMutation(
    (id: number) => apiClient.deleteMachine(id),
    {
      onSuccess: () => {
        success('设备删除成功');
        queryClient.invalidateQueries('machines');
      },
    }
  );

  const handleCreate = () => {
    setEditingMachine(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (machine: Machine) => {
    setEditingMachine(machine);
    setIsModalVisible(true);
    form.setFieldsValue({
      machine_name: machine.machine_name,
      skill_group_id: machine.skill_group_id,
      status: machine.status,
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingMachine) {
        updateMutation.mutate({ id: editingMachine.id, data: values });
      } else {
        createMutation.mutate(values);
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      available: { color: 'green', text: '可用' },
      busy: { color: 'blue', text: '忙碌' },
      maintenance: { color: 'orange', text: '维护中' },
      offline: { color: 'red', text: '离线' },
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 为全部设备标签页显示技能组列
  const allMachinesColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '设备名称',
      dataIndex: 'machine_name',
      key: 'machine_name',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '技能组',
      dataIndex: 'skill_group_name',
      key: 'skill_group_name',
      render: (text: string) => text ? getSkillGroupDisplayName(text) : '未分配',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: Machine) => (
        <Space>
          <Button
            type="link"
            icon={<SettingOutlined />}
            onClick={() => {/* TODO: Machine settings */}}
          >
            设置
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个设备吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 为技能组标签页隐藏技能组列
  const skillGroupColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '设备名称',
      dataIndex: 'machine_name',
      key: 'machine_name',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: Machine) => (
        <Space>
          <Button
            type="link"
            icon={<SettingOutlined />}
            onClick={() => {/* TODO: Machine settings */}}
          >
            设置
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个设备吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 根据当前标签页选择列配置
  const getCurrentColumns = () => {
    return activeTab === 'all' ? allMachinesColumns : skillGroupColumns;
  };

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            设备管理
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            新建设备
          </Button>
        </div>
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          type="card"
          size="small"
        >
          <TabPane
            tab={
              <span>
                <ToolOutlined />
                全部设备
                <Badge
                  count={machines?.length || 0}
                  style={{ marginLeft: 8 }}
                  showZero
                />
              </span>
            }
            key="all"
          >
            <Table
              columns={getCurrentColumns()}
              dataSource={getCurrentTabMachines()}
              rowKey="id"
              loading={isLoading}
              pagination={{
                total: getCurrentTabMachines().length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>

          {skillGroups?.map(skillGroup => (
            <TabPane
              tab={
                <span>
                  <ToolOutlined />
                  {getSkillGroupDisplayName(skillGroup.group_name)}
                  <Badge
                    count={machinesBySkillGroup[skillGroup.id.toString()]?.length || 0}
                    style={{ marginLeft: 8 }}
                    showZero
                  />
                </span>
              }
              key={skillGroup.id.toString()}
            >
              <Table
                columns={getCurrentColumns()}
                dataSource={getCurrentTabMachines()}
                rowKey="id"
                loading={isLoading}
                pagination={{
                  total: getCurrentTabMachines().length,
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`,
                }}
              />
            </TabPane>
          ))}
        </Tabs>
      </Card>

      <Modal
        title={editingMachine ? '编辑设备' : '新建设备'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingMachine(null);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading || updateMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            name="machine_name"
            label="设备名称"
            rules={[
              { required: true, message: '请输入设备名称' },
              { min: 2, message: '设备名称至少2个字符' },
            ]}
          >
            <Input placeholder="请输入设备名称" />
          </Form.Item>

          <Form.Item
            name="skill_group_id"
            label="技能组"
            rules={[
              { required: true, message: '请选择技能组' },
            ]}
          >
            <Select placeholder="请选择技能组">
              {skillGroups?.map((group: SkillGroup) => (
                <Option key={group.id} value={group.id}>
                  {getSkillGroupDisplayName(group.group_name)}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            initialValue="available"
          >
            <Select>
              <Option value="available">可用</Option>
              <Option value="busy">忙碌</Option>
              <Option value="maintenance">维护中</Option>
              <Option value="offline">离线</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Machines;
