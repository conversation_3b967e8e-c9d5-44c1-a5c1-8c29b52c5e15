import React, { useState, useMemo } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Popconfirm,
  Typography,
  Card,
  Tag,
  Tabs,
  Badge,
  Row,
  Col,
  Menu,
  Dropdown,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  ToolOutlined,
  MoreOutlined,
  FolderAddOutlined,
  FolderOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { useMessage } from '@/hooks/useMessage';
import type { Machine, CreateMachineRequest, SkillGroup, CreateSkillGroupRequest, SkillGroupDependencyInfo } from '@/types/api';

const { Title } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

const Machines: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingMachine, setEditingMachine] = useState<Machine | null>(null);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [isSkillGroupModalVisible, setIsSkillGroupModalVisible] = useState(false);
  const [editingSkillGroup, setEditingSkillGroup] = useState<SkillGroup | null>(null);
  const [form] = Form.useForm();
  const [skillGroupForm] = Form.useForm();
  const queryClient = useQueryClient();
  const { success, error } = useMessage();

  // 隐藏的技能组配置 - 这些技能组不会在设备管理中显示
  // 适用于没有设备的部门（如钳工等手工操作部门）
  const hiddenSkillGroups = [
    'Assembly',      // 装配 - 主要是手工操作
    'Quality Control', // 质检 - 主要使用检测工具而非生产设备
    // 可以根据需要添加更多需要隐藏的技能组
  ];

  // 技能组名称中文映射
  const getSkillGroupDisplayName = (groupName: string) => {
    const skillGroupNameMap: Record<string, string> = {
      'CNC Machining': 'CNC加工',
      'Milling': '铣削加工',
      'Turning': '车削加工',
      'Grinding': '磨削加工',
      'Assembly': '装配',
      'Quality Control': '质量控制',
    };
    return skillGroupNameMap[groupName] || groupName;
  };

  const { data: machines, isLoading } = useQuery(
    'machines',
    () => apiClient.getMachines()
  );

  const { data: skillGroups } = useQuery(
    'skill-groups',
    () => apiClient.getSkillGroups()
  );

  // 过滤掉隐藏的技能组，只显示有设备的技能组
  const visibleSkillGroups = useMemo(() => {
    if (!skillGroups) return [];
    return skillGroups.filter(group => !hiddenSkillGroups.includes(group.group_name));
  }, [skillGroups]);

  // 按技能组分组设备
  const machinesBySkillGroup = useMemo(() => {
    if (!machines || !skillGroups) return {};

    const grouped: Record<string, Machine[]> = {};

    // 初始化所有技能组（包括隐藏的，因为设备可能仍然关联到隐藏的技能组）
    skillGroups.forEach(group => {
      grouped[group.id.toString()] = [];
    });

    // 分组设备
    machines.forEach(machine => {
      const groupId = machine.skill_group_id.toString();
      if (grouped[groupId]) {
        grouped[groupId].push(machine);
      }
    });

    return grouped;
  }, [machines, skillGroups]);

  // 获取当前标签页的设备数据
  const getCurrentTabMachines = () => {
    if (activeTab === 'all') {
      return machines || [];
    }
    return machinesBySkillGroup[activeTab] || [];
  };

  const createMutation = useMutation(
    (data: CreateMachineRequest) => apiClient.createMachine(data),
    {
      onSuccess: () => {
        showMessage.success('设备创建成功');
        queryClient.invalidateQueries('machines');
        setIsModalVisible(false);
        form.resetFields();
      },
    }
  );

  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<CreateMachineRequest> }) =>
      apiClient.updateMachine(id, data),
    {
      onSuccess: () => {
        showMessage.success('设备更新成功');
        queryClient.invalidateQueries('machines');
        setIsModalVisible(false);
        setEditingMachine(null);
        form.resetFields();
      },
    }
  );

  const deleteMutation = useMutation(
    (id: number) => apiClient.deleteMachine(id),
    {
      onSuccess: () => {
        showMessage.success('设备删除成功');
        queryClient.invalidateQueries('machines');
      },
    }
  );

  // 技能组管理的mutations
  const createSkillGroupMutation = useMutation(
    (data: CreateSkillGroupRequest) => apiClient.createSkillGroup(data),
    {
      onSuccess: () => {
        showMessage.success('技能组创建成功');
        queryClient.invalidateQueries('skill-groups');
        setIsSkillGroupModalVisible(false);
        skillGroupForm.resetFields();
      },
      onError: (error: any) => {
        showMessage.error(error.response?.data?.message || '创建技能组失败');
      },
    }
  );

  const updateSkillGroupMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<CreateSkillGroupRequest> }) =>
      apiClient.updateSkillGroup(id, data),
    {
      onSuccess: () => {
        showMessage.success('技能组更新成功');
        queryClient.invalidateQueries('skill-groups');
        setIsSkillGroupModalVisible(false);
        setEditingSkillGroup(null);
        skillGroupForm.resetFields();
      },
      onError: (error: any) => {
        showMessage.error(error.response?.data?.message || '更新技能组失败');
      },
    }
  );

  const deleteSkillGroupMutation = useMutation(
    (id: number) => apiClient.deleteSkillGroup(id),
    {
      onSuccess: () => {
        showMessage.success('技能组删除成功');
        queryClient.invalidateQueries('skill-groups');
        queryClient.invalidateQueries('machines');
        // 如果删除的是当前活动标签页，切换到全部设备
        if (activeTab !== 'all') {
          setActiveTab('all');
        }
      },
      onError: (error: any) => {
        showMessage.error(error.response?.data?.message || '删除技能组失败');
      },
    }
  );

  const handleCreate = () => {
    setEditingMachine(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (machine: Machine) => {
    setEditingMachine(machine);
    setIsModalVisible(true);
    form.setFieldsValue({
      machine_name: machine.machine_name,
      skill_group_id: machine.skill_group_id,
      status: machine.status,
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingMachine) {
        updateMutation.mutate({ id: editingMachine.id, data: values });
      } else {
        createMutation.mutate(values);
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  // 技能组管理的处理函数
  const handleCreateSkillGroup = () => {
    setEditingSkillGroup(null);
    setIsSkillGroupModalVisible(true);
    skillGroupForm.resetFields();
  };

  const handleEditSkillGroup = (skillGroup: SkillGroup) => {
    setEditingSkillGroup(skillGroup);
    setIsSkillGroupModalVisible(true);
    skillGroupForm.setFieldsValue({
      group_name: skillGroup.group_name,
    });
  };

  const handleSkillGroupSubmit = async () => {
    try {
      const values = await skillGroupForm.validateFields();
      if (editingSkillGroup) {
        updateSkillGroupMutation.mutate({ id: editingSkillGroup.id, data: values });
      } else {
        createSkillGroupMutation.mutate(values);
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleDeleteSkillGroup = async (skillGroup: SkillGroup) => {
    try {
      // 先检查依赖关系
      const dependencyInfo = await apiClient.checkSkillGroupDependencies(skillGroup.id);

      if (!dependencyInfo.can_delete) {
        Modal.error({
          title: '无法删除技能组',
          content: (
            <div>
              <p>技能组 "{getSkillGroupDisplayName(skillGroup.group_name)}" 无法删除：</p>
              <p style={{ color: '#ff4d4f', marginTop: 8 }}>
                {dependencyInfo.blocking_reason}
              </p>
              {dependencyInfo.user_count > 0 && (
                <p>• 有 {dependencyInfo.user_count} 个用户使用此技能组</p>
              )}
              {dependencyInfo.machine_count > 0 && (
                <p>• 有 {dependencyInfo.machine_count} 台设备使用此技能组</p>
              )}
              {dependencyInfo.plan_task_count > 0 && (
                <p>• 有 {dependencyInfo.plan_task_count} 个生产任务使用此技能组</p>
              )}
            </div>
          ),
          okText: '知道了',
        });
        return;
      }

      // 如果可以删除，显示确认对话框
      Modal.confirm({
        title: '确定要删除这个技能组吗？',
        content: `删除技能组 "${getSkillGroupDisplayName(skillGroup.group_name)}" 后，此操作无法撤销。`,
        okText: '确定删除',
        cancelText: '取消',
        okType: 'danger',
        onOk: () => {
          deleteSkillGroupMutation.mutate(skillGroup.id);
        },
      });
    } catch (error: any) {
      showMessage.error(error.response?.data?.message || '检查技能组依赖关系失败');
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      available: { color: 'green', text: '可用' },
      busy: { color: 'blue', text: '忙碌' },
      maintenance: { color: 'orange', text: '维护中' },
      offline: { color: 'red', text: '离线' },
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 为全部设备标签页显示技能组列
  const allMachinesColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '设备名称',
      dataIndex: 'machine_name',
      key: 'machine_name',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '技能组',
      dataIndex: 'skill_group_name',
      key: 'skill_group_name',
      render: (text: string) => text ? getSkillGroupDisplayName(text) : '未分配',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: Machine) => (
        <Space>
          <Button
            type="link"
            icon={<SettingOutlined />}
            onClick={() => {/* TODO: Machine settings */}}
          >
            设置
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个设备吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 为技能组标签页隐藏技能组列
  const skillGroupColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '设备名称',
      dataIndex: 'machine_name',
      key: 'machine_name',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: Machine) => (
        <Space>
          <Button
            type="link"
            icon={<SettingOutlined />}
            onClick={() => {/* TODO: Machine settings */}}
          >
            设置
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个设备吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 根据当前标签页选择列配置
  const getCurrentColumns = () => {
    return activeTab === 'all' ? allMachinesColumns : skillGroupColumns;
  };

  // 生成技能组菜单项的下拉菜单
  const getSkillGroupDropdownMenu = (skillGroup: SkillGroup) => (
    <Menu>
      <Menu.Item
        key="edit"
        icon={<EditOutlined />}
        onClick={() => handleEditSkillGroup(skillGroup)}
      >
        编辑分组
      </Menu.Item>
      <Menu.Item
        key="delete"
        icon={<DeleteOutlined />}
        danger
        onClick={() => handleDeleteSkillGroup(skillGroup)}
      >
        删除分组
      </Menu.Item>
    </Menu>
  );

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            设备管理
          </Title>
          <Space>
            <Button
              icon={<FolderAddOutlined />}
              onClick={handleCreateSkillGroup}
            >
              新建分组
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              新建设备
            </Button>
          </Space>
        </div>
      </div>

      <Row gutter={16}>
        {/* 左侧分组导航 */}
        <Col xs={24} sm={6} md={5} lg={4}>
          <Card
            title="设备分组"
            size="small"
            style={{ height: 'fit-content' }}
          >
            <div style={{ marginBottom: 8 }}>
              <div
                style={{
                  padding: '8px 12px',
                  cursor: 'pointer',
                  borderRadius: '6px',
                  backgroundColor: activeTab === 'all' ? '#e6f7ff' : 'transparent',
                  border: activeTab === 'all' ? '1px solid #91d5ff' : '1px solid transparent',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
                onClick={() => setActiveTab('all')}
              >
                <Space>
                  <ToolOutlined />
                  <span>全部设备</span>
                </Space>
                <Badge
                  count={machines?.length || 0}
                  style={{ backgroundColor: '#52c41a' }}
                  showZero
                />
              </div>
            </div>

            {skillGroups?.map(skillGroup => (
              <div key={skillGroup.id} style={{ marginBottom: 8 }}>
                <div
                  style={{
                    padding: '8px 12px',
                    cursor: 'pointer',
                    borderRadius: '6px',
                    backgroundColor: activeTab === skillGroup.id.toString() ? '#e6f7ff' : 'transparent',
                    border: activeTab === skillGroup.id.toString() ? '1px solid #91d5ff' : '1px solid transparent',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                  onClick={() => setActiveTab(skillGroup.id.toString())}
                >
                  <Space>
                    <FolderOutlined />
                    <span>{getSkillGroupDisplayName(skillGroup.group_name)}</span>
                  </Space>
                  <Space>
                    <Badge
                      count={machinesBySkillGroup[skillGroup.id.toString()]?.length || 0}
                      style={{ backgroundColor: '#1890ff' }}
                      showZero
                    />
                    <Dropdown
                      overlay={getSkillGroupDropdownMenu(skillGroup)}
                      trigger={['click']}
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Button
                        type="text"
                        size="small"
                        icon={<MoreOutlined />}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </Dropdown>
                  </Space>
                </div>
              </div>
            ))}
          </Card>
        </Col>

        {/* 右侧设备列表 */}
        <Col xs={24} sm={18} md={19} lg={20}>
          <Card>
            <Table
              columns={getCurrentColumns()}
              dataSource={getCurrentTabMachines()}
              rowKey="id"
              loading={isLoading}
              pagination={{
                total: getCurrentTabMachines().length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </Card>
        </Col>
      </Row>

      <Modal
        title={editingMachine ? '编辑设备' : '新建设备'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingMachine(null);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading || updateMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            name="machine_name"
            label="设备名称"
            rules={[
              { required: true, message: '请输入设备名称' },
              { min: 2, message: '设备名称至少2个字符' },
            ]}
          >
            <Input placeholder="请输入设备名称" />
          </Form.Item>

          <Form.Item
            name="skill_group_id"
            label="技能组"
            rules={[
              { required: true, message: '请选择技能组' },
            ]}
          >
            <Select placeholder="请选择技能组">
              {skillGroups?.map((group: SkillGroup) => (
                <Option key={group.id} value={group.id}>
                  {getSkillGroupDisplayName(group.group_name)}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            initialValue="available"
          >
            <Select>
              <Option value="available">可用</Option>
              <Option value="busy">忙碌</Option>
              <Option value="maintenance">维护中</Option>
              <Option value="offline">离线</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 技能组管理Modal */}
      <Modal
        title={editingSkillGroup ? '编辑技能组' : '新建技能组'}
        open={isSkillGroupModalVisible}
        onOk={handleSkillGroupSubmit}
        onCancel={() => {
          setIsSkillGroupModalVisible(false);
          setEditingSkillGroup(null);
          skillGroupForm.resetFields();
        }}
        confirmLoading={createSkillGroupMutation.isLoading || updateSkillGroupMutation.isLoading}
      >
        <Form
          form={skillGroupForm}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            name="group_name"
            label="技能组名称"
            rules={[
              { required: true, message: '请输入技能组名称' },
              { min: 2, message: '技能组名称至少2个字符' },
              { max: 50, message: '技能组名称不能超过50个字符' },
            ]}
          >
            <Input placeholder="请输入技能组名称" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Machines;
